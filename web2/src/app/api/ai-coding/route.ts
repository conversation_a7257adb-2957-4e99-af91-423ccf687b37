import { NextRequest, NextResponse } from 'next/server';
import { type Message, LangChainAdapter } from 'ai';
import { graph as designToCodeGraph } from "../../../graph/designToCode"
import { graph as codingChatGraph } from "../../../graph/codingChat"
import { DesignItem } from "../../../graph/designToCode/types";
import { v4 as uuidv4 } from "uuid";

import { convertVercelMessageToLangChainMessage } from '@/utils/message-converters';
import { logToolCallsInDevelopment } from '@/utils/stream-logging';

/**
 * This handler initializes and calls the designToCode agent for AI coding tasks.
 * It supports file uploads and converts them to DesignItem format for processing.
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    console.log('AI Coding API: 收到请求', {
      messagesCount: body.messages?.length || 0,
      hasMessages: !!body.messages,
      hasFiles: !!body.files && body.files.length > 0,
      filesCount: body.files?.length || 0
    });

    /**
     * We represent intermediate steps as system messages for display purposes,
     * but don't want them in the chat history.
     */
    const messages = (body.messages ?? [])
      .filter((message: Message) => message.role === 'user' || message.role === 'assistant')
      .map(convertVercelMessageToLangChainMessage);

    console.log('AI Coding API: 处理后的消息', {
      messagesCount: messages.length,
      lastMessage: messages[messages.length - 1]?.content
    });

    // 获取最后一条用户消息作为输入
    const lastUserMessage = messages
      .filter((msg: any) => msg.getType() === 'human')
      .pop();

    let userInput = (lastUserMessage?.content as string) || '';

    // 处理文件上传，判断使用哪个graph
    const files = body.files || [];

    // 判断是否有图片文件（设计稿）
    const hasImageFiles = files.some((file: any) => {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(fileExtension || '');
    });

    console.log('AI Coding API: 文件分析', {
      filesCount: files.length,
      hasImageFiles,
      fileTypes: files.map((f: any) => f.name.split('.').pop()?.toLowerCase())
    });

    // 如果有图片文件，使用设计稿转代码的流程
    if (hasImageFiles) {
      return handleDesignToCode(files, userInput, messages);
    } else {
      // 否则使用编程聊天流程
      return handleCodingChat(files, userInput, messages);
    }

  } catch (e: any) {
    console.error('AI Coding API: 错误', e);
    return NextResponse.json({ error: e.message }, { status: e.status ?? 500 });
  }
}

/**
 * 处理设计稿转代码的请求
 */
async function handleDesignToCode(files: any[], userInput: string, messages: any[]) {
  console.log('AI Coding API: 使用设计稿转代码流程');

  const designItems: DesignItem[] = [];

  // 将文件转换为DesignItem格式
  files.forEach((file: any) => {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const isImageFile = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(fileExtension || '');

    designItems.push({
      name: file.name,
      content: file.content,
      type: isImageFile ? "img" : "html"
    });
  });

  const graphInput = {
    messages: messages,
    input: designItems,
    output: ''
  };

  console.log('AI Coding API: 调用 designToCode graph', {
    userInput: userInput.substring(0, 200) + (userInput.length > 200 ? '...' : ''),
    messagesCount: messages.length,
    designItemsCount: designItems.length
  });

  const threadId = uuidv4();
  const eventStream = designToCodeGraph.streamEvents(graphInput, {
    version: 'v2',
    configurable: { thread_id: threadId }
  });

  const transformedStream = logToolCallsInDevelopment(eventStream);
  return LangChainAdapter.toDataStreamResponse(transformedStream);
}

/**
 * 处理编程聊天的请求
 */
async function handleCodingChat(files: any[], userInput: string, messages: any[]) {
  console.log('AI Coding API: 使用编程聊天流程');

  // 如果没有用户输入，提供默认提示
  if (!userInput.trim() && files.length > 0) {
    userInput = `请分析以下上传的文件并提供编程建议或优化方案。`;
  }

  const graphInput = {
    messages: messages,
    input: userInput,
    output: '',
    files: files.map((file: any) => ({
      name: file.name,
      content: file.content
    }))
  };

  console.log('AI Coding API: 调用 codingChat graph', {
    userInput: userInput.substring(0, 200) + (userInput.length > 200 ? '...' : ''),
    messagesCount: messages.length,
    filesCount: files.length
  });

  const threadId = uuidv4();
  const eventStream = codingChatGraph.streamEvents(graphInput, {
    version: 'v2',
    configurable: { thread_id: threadId }
  });

  const transformedStream = logToolCallsInDevelopment(eventStream);
  return LangChainAdapter.toDataStreamResponse(transformedStream);
}
