import { StateGraph, END, START, Annotation } from "@langchain/langgraph";
import { SystemMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import type { BaseMessageLike } from "@langchain/core/messages";
import { createModel } from "../designToCode/model/index";

const CodingChatStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessageLike[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  input: Annotation<string>({
    reducer: (x, y) => y || x,
    default: () => "",
  }),
  output: Annotation<string>({
    reducer: (x, y) => y || x,
    default: () => "",
  }),
  // 可选的文件内容
  files: Annotation<Array<{name: string, content: string}>>({
    reducer: (x, y) => y || x,
    default: () => [],
  }),
});

/**
 * 编程聊天节点 - 专门处理编程相关的对话
 */
async function codingChatNode(state: typeof CodingChatStateAnnotation.State) {
  console.log("CodingChat: 处理编程相关消息...", {
    messagesCount: state.messages.length,
    input: state.input,
    filesCount: state.files?.length || 0
  });

  // 创建模型
  const model = createModel();

  // 构建消息列表 - 使用现有的消息历史
  let messages = [...state.messages];

  // 如果没有消息历史，添加系统消息
  if (messages.length === 0) {
    const systemPrompt = `你是一个专业的编程助手，擅长：
1. 代码编写和优化
2. 调试和问题解决
3. 代码审查和重构
4. 技术架构设计
5. 编程最佳实践指导
6. 多种编程语言和框架

请根据用户的编程需求提供专业、详细的帮助。如果用户上传了代码文件，请仔细分析并提供针对性的建议。`;

    messages.push(new SystemMessage(systemPrompt));
  }

  // 构建用户输入，包含文件信息
  let userInput = state.input || '';
  
  // 如果有文件，将文件内容添加到输入中
  if (state.files && state.files.length > 0) {
    const fileContents = state.files.map(file => 
      `\n\n--- 文件: ${file.name} ---\n${file.content}\n--- 文件结束 ---`
    ).join('');
    
    userInput = `${userInput}\n\n📎 上传的文件内容：${fileContents}`;
  }

  // 如果有额外的输入，添加为用户消息
  if (userInput && userInput.trim()) {
    messages.push(new HumanMessage(userInput));
  }

  console.log(`CodingChat: 调用模型处理 ${messages.length} 条消息`);

  try {
    const responseMessage = await model.invoke(messages);

    console.log("CodingChat: 模型响应成功", {
      responseLength: responseMessage.content?.toString().length || 0
    });

    return {
      messages: [responseMessage],
      output: responseMessage.content?.toString() || "",
    };
  } catch (error) {
    console.error("CodingChat: 模型调用失败:", error);

    const errorResponse = new AIMessage("抱歉，我在处理您的编程请求时遇到了问题，请稍后再试。");

    return {
      messages: [errorResponse],
      output: errorResponse.content as string,
    };
  }
}

// 构建编程聊天工作流图
const workflow = new StateGraph(CodingChatStateAnnotation)
  .addNode("codingChat", codingChatNode)
  .addEdge(START, "codingChat")
  .addEdge("codingChat", END);

// 编译工作流
export const graph = workflow.compile();
graph.name = "codingChat";

console.log("CodingChat: 图已创建并编译完成");
