'use client';

import { type Message } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useState } from 'react';
import type { FormEvent, ReactNode } from 'react';
import { toast, Toaster } from 'sonner';
import { StickToBottom, useStickToBottomContext } from 'use-stick-to-bottom';
import { ArrowDown, ArrowUpIcon, LoaderCircle } from 'lucide-react';

import { ChatMessageBubble } from '@/components/ChatMessageBubble';
import { ThinkingIndicator } from '@/components/ThinkingIndicator';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/utils/cn';

function ChatMessages(props: {
  messages: Message[];
  emptyStateComponent: ReactNode;
  aiEmoji?: string;
  className?: string;
  showThinking?: boolean;
}) {
  return (
    <div className="flex flex-col max-w-[800px] mx-auto pb-12 w-full">
      {props.messages.map((m) => {
        return <ChatMessageBubble key={m.id} message={m} aiEmoji={props.aiEmoji} thinkingMode="chat" />;
      })}
      {props.showThinking && (
        <div className="flex justify-center mt-6">
          <ThinkingIndicator isVisible={true} mode="chat" />
        </div>
      )}
    </div>
  );
}

function ScrollToBottom(props: { className?: string }) {
  const { isAtBottom, scrollToBottom } = useStickToBottomContext();

  if (isAtBottom) return null;
  return (
    <Button variant="outline" className={props.className} onClick={() => scrollToBottom()}>
      <ArrowDown className="w-4 h-4" />
      <span>Scroll to bottom</span>
    </Button>
  );
}

function ChatInput(props: {
  onSubmit: (e: FormEvent<HTMLFormElement>) => void;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  loading?: boolean;
  placeholder?: string;
  children?: ReactNode;
  className?: string;
}) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      props.onSubmit(e as unknown as FormEvent<HTMLFormElement>);
    }
  };

  return (
    <form
      onSubmit={(e) => {
        e.stopPropagation();
        e.preventDefault();
        props.onSubmit(e);
      }}
      className={cn('flex w-full flex-col', props.className)}
    >
      <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/50 max-w-[800px] w-full mx-auto overflow-hidden chat-input-shadow animate-scale-in">
        <div className="flex items-end p-4 gap-4">
          <Textarea
            value={props.value}
            placeholder={props.placeholder}
            onChange={props.onChange}
            onKeyDown={handleKeyDown}
            className="border-none outline-none bg-transparent flex-grow resize-none text-base leading-6 min-h-[24px] max-h-[200px] placeholder:text-gray-500 focus:placeholder:text-gray-400 transition-all duration-200"
            rows={1}
            style={{
              minHeight: '24px',
              maxHeight: '200px',
              lineHeight: '1.5'
            }}
          />

          <div className="flex items-center gap-2">
            {props.children}
            <Button
              className="rounded-full p-2 h-10 w-10 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-300 hover:scale-110 shadow-lg flex-shrink-0"
              type="submit"
              disabled={props.loading}
            >
              {props.loading ? <LoaderCircle className="animate-spin h-5 w-5" /> : <ArrowUpIcon className="h-5 w-5" />}
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
}

function StickyToBottomContent(props: {
  content: ReactNode;
  footer?: ReactNode;
  className?: string;
  contentClassName?: string;
}) {
  const context = useStickToBottomContext();

  return (
    <div
      ref={context.scrollRef}
      style={{ width: '100%', height: '100%' }}
      className={cn('grid grid-rows-[1fr,auto]', props.className)}
    >
      <div ref={context.contentRef} className={props.contentClassName}>
        {props.content}
      </div>

      {props.footer}
    </div>
  );
}

export function ChatWindow(props: {
  endpoint: string;
  emptyStateComponent: ReactNode;
  placeholder?: string;
  emoji?: string;
}) {
  const [showThinking, setShowThinking] = useState(false);

  const chat = useChat({
    api: props.endpoint,
    onFinish(response: Message) {
      console.log('Final response: ', response?.content);
      setShowThinking(false);
    },
    onResponse(response: Response) {
      console.log('Response received. Status:', response.status);
      setShowThinking(false);
    },
    onError: (e: Error) => {
      console.error('Error: ', e);
      setShowThinking(false);
      toast.error(`Error while processing your request`, { description: e.message });
    },
  });

  function isChatLoading(): boolean {
    return chat.status === 'streaming';
  }

  async function sendMessage(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (isChatLoading()) return;

    // 开始思考状态
    setShowThinking(true);

    chat.handleSubmit(e);
  }

  return (
    <>
      <StickToBottom>
        <StickyToBottomContent
          className="absolute inset-0"
          contentClassName="py-8 px-2"
          content={
            chat.messages.length === 0 ? (
              <div>{props.emptyStateComponent}</div>
            ) : (
              <ChatMessages
                aiEmoji={props.emoji}
                messages={chat.messages}
                emptyStateComponent={props.emptyStateComponent}
                showThinking={showThinking}
              />
            )
          }
          footer={
            <div className="sticky bottom-4 px-4">
              <ScrollToBottom className="absolute bottom-full left-1/2 -translate-x-1/2 mb-4" />
              <ChatInput
                value={chat.input}
                onChange={chat.handleInputChange}
                onSubmit={sendMessage}
                loading={isChatLoading()}
                placeholder={props.placeholder ?? 'What can I help you with?'}
              ></ChatInput>
            </div>
          }
        ></StickyToBottomContent>
      </StickToBottom>
      <Toaster />
    </>
  );
}
